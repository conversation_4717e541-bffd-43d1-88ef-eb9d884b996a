hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@colors/colors@1.6.0':
    '@colors/colors': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@hapi/bourne@3.0.0':
    '@hapi/bourne': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  accepts@1.3.8:
    accepts: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bytes@3.1.2:
    bytes: private
  cache-content-type@1.0.1:
    cache-content-type: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  cliui@8.0.1:
    cliui: private
  co-body@6.2.0:
    co-body: private
  co@4.6.0:
    co: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@3.2.1:
    color: private
  colorspace@1.1.4:
    colorspace: private
  combined-stream@1.0.8:
    combined-stream: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookies@0.9.1:
    cookies: private
  copy-to@2.0.1:
    copy-to: private
  date-fns@2.30.0:
    date-fns: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  deep-equal@1.0.1:
    deep-equal: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enabled@2.0.0:
    enabled: private
  encodeurl@1.0.2:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  fecha@4.2.3:
    fecha: private
  fill-range@7.1.1:
    fill-range: private
  fn.name@1.1.0:
    fn.name: private
  follow-redirects@1.15.11:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  fresh@0.5.2:
    fresh: private
  function-bind@1.1.2:
    function-bind: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  gopd@1.2.0:
    gopd: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http-assert@1.5.0:
    http-assert: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inflation@2.1.0:
    inflation: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-regex@1.2.1:
    is-regex: private
  is-stream@2.0.1:
    is-stream: private
  keygrip@1.1.0:
    keygrip: private
  koa-compose@4.1.0:
    koa-compose: private
  koa-convert@2.0.0:
    koa-convert: private
  koa-send@5.0.1:
    koa-send: private
  kuler@2.0.0:
    kuler: private
  lodash@4.17.21:
    lodash: private
  logform@2.7.0:
    logform: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  methods@1.1.2:
    methods: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  negotiator@0.6.3:
    negotiator: private
  normalize-path@3.0.0:
    normalize-path: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  one-time@1.0.0:
    one-time: private
  only@0.0.2:
    only: private
  parseurl@1.3.3:
    parseurl: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  picomatch@2.3.1:
    picomatch: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pstree.remy@1.1.8:
    pstree.remy: private
  qs@6.14.0:
    qs: private
  raw-body@2.5.2:
    raw-body: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  require-directory@2.1.1:
    require-directory: private
  resolve-path@1.4.0:
    resolve-path: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.2:
    semver: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  spawn-command@0.0.2:
    spawn-command: private
  stack-trace@0.0.10:
    stack-trace: private
  statuses@1.5.0:
    statuses: private
  string-width@4.2.3:
    string-width: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  supports-color@8.1.1:
    supports-color: private
  text-hex@1.0.0:
    text-hex: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  tree-kill@1.2.2:
    tree-kill: private
  triple-beam@1.4.1:
    triple-beam: private
  tslib@2.8.1:
    tslib: private
  tsscmp@1.0.6:
    tsscmp: private
  type-is@1.6.18:
    type-is: private
  undefsafe@2.0.5:
    undefsafe: private
  unpipe@1.0.0:
    unpipe: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vary@1.1.2:
    vary: private
  winston-transport@4.9.0:
    winston-transport: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  ylru@1.4.0:
    ylru: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Fri, 01 Aug 2025 03:16:09 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\zhangzixuan1\homework-3\server\node_modules\.pnpm
virtualStoreDirMaxLength: 60
