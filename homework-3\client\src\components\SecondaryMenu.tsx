import React, { useState } from "react";
import { Bold, Italic, Minus, ChevronUp, ChevronDown } from "lucide-react";
import { SketchPicker } from "react-color";
import { Button } from "../components/ui/button";
import { Separator } from "../components/ui/separator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../components/ui/popover";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../components/ui/tooltip";
import type { MenuType } from "../types/mindmap";
import { useMindMapStore } from "../store/mindMapStore";
import "./SecondaryMenu.css";
import { style_icons, start_t_icons, start_b_icons } from "../config/constants";
import { presetColors } from "../config/colors";

interface SecondaryMenuProps {
  menuType: MenuType;
}

const borderWidths = [0, 1, 2, 3, 4, 5]; // 0 表示"无"

export const SecondaryMenu = ({ menuType }: SecondaryMenuProps) => {
  const [borderWidthOpen, setBorderWidthOpen] = useState(false);
  const [colorPickerOpen, setColorPickerOpen] = useState(false);

  // 使用全局状态管理
  const {
    selectedNode,
    mindMapData,
    toggleBold,
    toggleItalic,
    updateBorderWidth,
    updateTextColor,
  } = useMindMapStore();

  const currentNode = selectedNode ? mindMapData.nodes[selectedNode.id] : null;
  const isNodeSelected = !!selectedNode;

  const TooltipButton = ({
    icon,
    label,
    onClick,
    disabled = false,
    size = "sm",
    variant = "ghost",
    showDropdownArrow = true,
    iconOnly = false,
  }: {
    icon: React.ReactNode; // 按钮的图标
    label: string; // 按钮的文本
    onClick: () => void; // 点击按钮时调用的事件
    disabled?: boolean; // 是否禁用按钮
    size?: "icon" | "sm" | "default" | "lg"; // 按钮大小
    variant?:
      | "ghost"
      | "link"
      | "default"
      | "destructive"
      | "outline"
      | "secondary"; // 按钮样式类型
    showDropdownArrow?: boolean; // 是否显示下拉箭头
    iconOnly?: boolean; // 是否只显示图标
  }) => {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            disabled={disabled} // 如果节点未选中，按钮会禁用
            className="button-small" // 按钮样式
            onClick={onClick} // 点击事件
          >
            {icon && <span className="icon-small">{icon}</span>}
            {!iconOnly && <span className="text-small">{label}</span>}
            {!iconOnly && showDropdownArrow && (
              <div className="margin-left-medium">
                <ChevronDown className="dropdown-arrow" />
              </div>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>{label}</TooltipContent> {/* 显示 Tooltip 内容 */}
      </Tooltip>
    );
  };

  const renderStartMenu = () => (
    <div className="menu-container">
      <TooltipProvider delayDuration={0}>
        {start_t_icons.map(
          ({ icon, label, onClick, showDropdownArrow, iconOnly }, index) => (
            <React.Fragment key={label}>
              {/* 如果前一个图标有 addSeparator 属性，则在当前图标前显示分隔符 */}
              {index > 0 && start_t_icons[index - 1].addSeparator && (
                <Separator orientation="vertical" className="separator" />
              )}
              <div className="button-container">
                <TooltipButton
                  icon={icon ? <img src={icon} alt={label} /> : null} // 只有当icon不为空时才显示图标
                  label={label} // 文本
                  onClick={onClick} // 传入按钮点击时调用的函数
                  disabled={!isNodeSelected} // 如果没有选中节点，按钮禁用
                  showDropdownArrow={showDropdownArrow} // 是否显示下拉箭头
                  iconOnly={iconOnly} // 是否只显示图标
                />
              </div>
            </React.Fragment>
          )
        )}
        <Separator orientation="vertical" className="separator" />
        {/* 加粗按钮 */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              disabled={!isNodeSelected}
              onClick={() => selectedNode && toggleBold(selectedNode.id)}
              className={`button-square ${currentNode?.style.fontWeight === "bold" ? "button-active" : ""}`}
            >
              <Bold className="icon-small" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>加粗（Crtl+B）</TooltipContent>
        </Tooltip>

        {/* 斜体按钮 */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              disabled={!isNodeSelected}
              onClick={() => selectedNode && toggleItalic(selectedNode.id)}
              className={`button-square ${currentNode?.style.fontStyle === "italic" ? "button-active" : ""}`}
            >
              <Italic className="icon-small" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>斜体（Crtl+I）</TooltipContent>
        </Tooltip>

        {/* 字体颜色 */}
        <Popover open={colorPickerOpen} onOpenChange={setColorPickerOpen}>
          <Tooltip>
            <TooltipTrigger asChild>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={!isNodeSelected}
                  className={`button-square ${colorPickerOpen ? "button-active" : ""}`}
                >
                  <svg
                    className="icon icon-small"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                  >
                    <path
                      d="M825.6 652.8L544 83.2C537.6 70.4 524.8 64 512 64s-25.6 6.4-32 19.2l-281.6 569.6c-6.4 19.2 0 38.4 19.2 51.2 19.2 6.4 38.4 0 51.2-19.2L384 454.4h275.2l115.2 230.4c6.4 19.2 32 25.6 51.2 19.2 6.4-12.8 12.8-32 0-51.2zM409.6 384L512 172.8 614.4 384H409.6z"
                      fill="#2c2c2c"
                      p-id="9382"
                    ></path>
                    <path
                      d="M876.8 960H147.2c-44.8 0-83.2-38.4-83.2-83.2v-19.2c0-51.2 38.4-89.6 83.2-89.6h723.2c44.8 0 83.2 38.4 83.2 83.2v19.2c6.4 51.2-32 89.6-76.8 89.6z"
                      p-id="9383"
                      data-spm-anchor-id="a313x.search_index.0.i19.4e3e3a81nzKYDC"
                      fill={currentNode?.style.color || "#000000"}
                    ></path>
                  </svg>{" "}
                  <div className="margin-left-medium">
                    {colorPickerOpen ? (
                      <ChevronUp className="dropdown-arrow" />
                    ) : (
                      <ChevronDown className="dropdown-arrow" />
                    )}
                  </div>
                </Button>
              </PopoverTrigger>
            </TooltipTrigger>
            <TooltipContent>字体颜色</TooltipContent>
          </Tooltip>
          <PopoverContent className="popover-content-color">
            <SketchPicker
              color={currentNode?.style.color || "#000000"}
              onChange={(color) => {
                if (selectedNode) {
                  updateTextColor(selectedNode.id, color.hex);
                }
              }}
              onChangeComplete={() => {
                setColorPickerOpen(false);
              }}
              disableAlpha={true}
              presetColors={presetColors}
            />
          </PopoverContent>
        </Popover>
        <Separator orientation="vertical" className="separator" />

        {start_b_icons.map(
          ({ icon, label, onClick, showDropdownArrow, iconOnly }, index) => (
            <React.Fragment key={label}>
              {/* 如果前一个图标有 addSeparator 属性，则在当前图标前显示分隔符 */}
              {index > 0 && start_b_icons[index - 1].addSeparator && (
                <Separator orientation="vertical" className="separator" />
              )}
              <div className="button-container">
                <TooltipButton
                  icon={icon ? <img src={icon} alt={label} /> : null} // 只有当icon不为空时才显示图标
                  label={label} // 文本
                  onClick={onClick} // 传入按钮点击时调用的函数
                  disabled={!isNodeSelected} // 如果没有选中节点，按钮禁用
                  showDropdownArrow={showDropdownArrow} // 是否显示下拉箭头
                  iconOnly={iconOnly} // 是否只显示图标
                />
              </div>
            </React.Fragment>
          )
        )}
      </TooltipProvider>
    </div>
  );

  const renderStyleMenu = () => (
    <div className="menu-container">
      <TooltipProvider delayDuration={0}>
        {style_icons.map(
          ({ icon, label, onClick, showDropdownArrow, iconOnly }, index) => (
            <React.Fragment key={label}>
              {/* 如果前一个图标有 addSeparator 属性，则在当前图标前显示分隔符 */}
              {index > 0 && style_icons[index - 1].addSeparator && (
                <Separator orientation="vertical" className="separator" />
              )}
              <div className="button-container">
                {/* 如果是边框宽度，显示边框宽度选择器 */}
                {label === "边框宽度" ? (
                  <div className="flex-center-gap-small">
                    <Popover
                      open={borderWidthOpen}
                      onOpenChange={setBorderWidthOpen}
                    >
                      <Tooltip open={!borderWidthOpen}>
                        <PopoverTrigger asChild>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              disabled={!isNodeSelected}
                              className={`button-small ${borderWidthOpen ? "button-active" : ""}`}
                            >
                              {icon && (
                                <span className="icon-small">
                                  <img src={icon} alt={label} />
                                </span>
                              )}
                              {!iconOnly && (
                                <span className="text-small">{label}</span>
                              )}
                              <div className="margin-left-medium">
                                {borderWidthOpen ? (
                                  <ChevronUp className="dropdown-arrow" />
                                ) : (
                                  <ChevronDown className="dropdown-arrow" />
                                )}
                              </div>
                            </Button>
                          </TooltipTrigger>
                        </PopoverTrigger>
                        <TooltipContent>边框宽度</TooltipContent>
                      </Tooltip>
                      <PopoverContent className="popover-content-border">
                        <div className="border-width-list">
                          {borderWidths.map((width) => {
                            const isSelected =
                              currentNode?.style.borderWidth === width;
                            const displayText =
                              width === 0 ? "无" : `${width}px`;

                            return (
                              <Button
                                key={width}
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  if (selectedNode) {
                                    updateBorderWidth(selectedNode.id, width);
                                  }
                                  setBorderWidthOpen(false);
                                }}
                                className={`border-width-item ${isSelected ? "border-width-selected" : ""}`}
                              >
                                <svg
                                  className={`check-icon ${isSelected ? "check-icon-visible" : "check-icon-hidden"}`}
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <polyline points="20,6 9,17 4,12"></polyline>
                                </svg>
                                <span className="border-width-text">
                                  {displayText}
                                </span>
                              </Button>
                            );
                          })}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                ) : (
                  <TooltipButton
                    icon={icon ? <img src={icon} alt={label} /> : null} // 只有当icon不为空时才显示图标
                    label={label} // 文本
                    onClick={onClick} // 传入按钮点击时调用的函数
                    disabled={!isNodeSelected} // 如果没有选中节点，按钮禁用
                    showDropdownArrow={showDropdownArrow} // 是否显示下拉箭头
                    iconOnly={iconOnly} // 是否只显示图标
                  />
                )}
              </div>
            </React.Fragment>
          )
        )}
      </TooltipProvider>
    </div>
  );

  const renderOtherMenus = () => (
    <div className="menu-container-wide">
      <span className="text-small text-gray">
        {menuType === "insert" && "插入功能"}
        {menuType === "view" && "视图功能"}
        {menuType === "export" && "导出功能"}
      </span>
    </div>
  );

  return (
    <div className="secondary-menu">
      {menuType === "start" && renderStartMenu()}
      {menuType === "style" && renderStyleMenu()}
      {(menuType === "insert" ||
        menuType === "view" ||
        menuType === "export") &&
        renderOtherMenus()}
    </div>
  );
};
