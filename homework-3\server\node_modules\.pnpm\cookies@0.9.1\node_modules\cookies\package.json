{"name": "cookies", "description": "Cookies, optionally signed using Keygrip.", "version": "0.9.1", "author": "<PERSON> <<EMAIL>> (http://jed.is)", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/cookies", "dependencies": {"depd": "~2.0.0", "keygrip": "~1.1.0"}, "devDependencies": {"eslint": "8.56.0", "express": "4.18.2", "mocha": "10.2.0", "nyc": "15.1.0", "restify": "8.6.1", "supertest": "6.3.3"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}