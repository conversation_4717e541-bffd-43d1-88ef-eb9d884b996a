<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>思维导图AI节点生成测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .prompt-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 2px 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .node-item {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #007bff;
        }
        .node-item.level-2 {
            border-left-color: #28a745;
            margin-left: 20px;
        }
        .node-item.level-3 {
            border-left-color: #ffc107;
            margin-left: 40px;
        }
        .node-meta {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 思维导图AI节点生成测试</h1>
        
        <div class="instructions">
            <h4>📋 功能说明</h4>
            <p>这个测试页面用于验证AI思维导图节点生成功能：</p>
            <ol>
                <li><strong>流式生成</strong>：AI返回一个节点数据就立即渲染一个节点</li>
                <li><strong>结构化数据</strong>：AI返回的是JSON格式的节点数据，包含id、text、level、parentId</li>
                <li><strong>层级关系</strong>：支持level 2和level 3节点，自动建立父子关系</li>
                <li><strong>实时布局</strong>：新增节点后自动调整思维导图布局</li>
            </ol>
            <p><strong>使用方法：</strong></p>
            <ol>
                <li>确保服务器运行在 http://localhost:3001</li>
                <li>在React应用中双击level 1节点进入编辑模式</li>
                <li>输入主题（如"计算机原理"）</li>
                <li>右键点击该节点，选择"AI创作"</li>
                <li>观察AI流式生成子节点的过程</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 AI思维导图节点生成测试</h3>
            <p>输入一个主题，AI将生成相关的思维导图节点结构：</p>
            <textarea class="prompt-input" id="testPrompt" placeholder="输入主题，例如：计算机原理、前端开发、机器学习等" rows="2"></textarea>
            <button onclick="testAiMindMapNodes()" id="generateBtn">🧠 生成思维导图节点</button>
            <button onclick="clearResults()" id="clearBtn">🗑️ 清空结果</button>
            <div id="testStatus"></div>
            <div id="mindMapNodes" class="output" style="display: none;">
                <h4>📊 生成的节点：</h4>
                <div id="nodesList"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 实现细节</h3>
            <p><strong>新增的核心文件和方法：</strong></p>
            <ul>
                <li><span class="code">addAiGeneratedNode()</span> - 在mindMapStore中添加AI生成的节点</li>
                <li><span class="code">generateAiMindMapNodes()</span> - 专门用于思维导图的AI生成函数</li>
                <li><span class="code">AIMindMapCallbacks</span> - 流式渲染的回调接口</li>
            </ul>
            
            <p><strong>流式处理逻辑：</strong></p>
            <pre class="code">
// AI返回的数据格式示例：
{
  "id": "2",
  "text": "硬件",
  "level": 2,
  "parentId": "1"
}
STOP
{
  "id": "3",
  "text": "中央处理器(CPU)",
  "level": 3,
  "parentId": "2"
}
STOP
            </pre>
            
            <p><strong>关键特性：</strong></p>
            <ul>
                <li>✅ <strong>流式渲染</strong>：每解析到一个完整的JSON节点就立即添加到思维导图</li>
                <li>✅ <strong>自动布局</strong>：新增节点后自动调用relayoutLevel2Nodes或relayoutLevel3Nodes</li>
                <li>✅ <strong>错误处理</strong>：处理不完整的JSON数据，等待更多数据到达</li>
                <li>✅ <strong>层级管理</strong>：支持level 2和level 3节点，自动建立父子关系</li>
            </ul>
        </div>
    </div>

    <script>
        let isGenerating = false;
        let generatedNodes = [];

        // 模拟AI思维导图节点生成函数
        async function generateAiMindMapNodes(prompt, parentNodeId, callbacks = {}) {
            console.log("🧠 开始AI思维导图节点生成...", { prompt, parentNodeId });

            if (!prompt || prompt.trim() === '') {
                throw new Error('prompt不能为空');
            }

            const generatedNodes = [];
            let buffer = '';

            // ID映射表：AI返回的逻辑ID -> 实际的节点ID
            const idMapping = {
                '1': parentNodeId, // AI返回的"1"映射到实际的父节点ID
            };

            try {
                // 调用AI接口
                const response = await fetch('http://localhost:3001/api/chatStream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: prompt.trim() }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (!response.body) {
                    throw new Error('Response body is null');
                }

                console.log("📡 开始接收SSE流式响应...");
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let sseBuffer = '';

                callbacks.onStart?.();

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        console.log("✅ 流式响应结束");
                        break;
                    }

                    sseBuffer += decoder.decode(value, { stream: true });
                    const lines = sseBuffer.split('\n\n');
                    sseBuffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.trim() === '') continue;
                        
                        const dataMatch = line.match(/^data: (.+)$/m);
                        
                        if (dataMatch) {
                            try {
                                const parsedData = JSON.parse(dataMatch[1]);
                                if (parsedData.text) {
                                    console.log("📝 接收到数据片段:", parsedData.text);
                                    buffer += parsedData.text;

                                    // 使用STOP作为分隔符来分割完整的JSON对象
                                    const parts = buffer.split('STOP');
                                    buffer = parts.pop() || ''; // 保留最后一部分（可能不完整）

                                    for (const part of parts) {
                                        const trimmedPart = part.trim();
                                        if (trimmedPart === '' || trimmedPart === 'END') {
                                            continue;
                                        }

                                        try {
                                            const nodeData = JSON.parse(trimmedPart);

                                            if (nodeData.id && nodeData.text && nodeData.level) {
                                                // 跳过level 1节点（根节点）
                                                if (nodeData.level === 1) {
                                                    console.log("⏭️ 跳过level 1节点:", nodeData);
                                                    continue;
                                                }

                                                // 映射AI返回的parentId到实际的节点ID
                                                let actualParentId = nodeData.parentId || parentNodeId;
                                                if (nodeData.parentId && idMapping[nodeData.parentId]) {
                                                    actualParentId = idMapping[nodeData.parentId];
                                                    console.log(`🔗 ID映射: AI的"${nodeData.parentId}" -> 实际的"${actualParentId}"`);
                                                }

                                                // 生成实际的节点ID
                                                const actualNodeId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                                                // 将AI的ID映射到实际的节点ID，供后续节点使用
                                                idMapping[nodeData.id] = actualNodeId;

                                                const mindMapNode = {
                                                    id: actualNodeId,
                                                    text: nodeData.text,
                                                    level: nodeData.level,
                                                    parentId: actualParentId,
                                                };

                                                console.log("✅ 解析到完整节点:", mindMapNode);
                                                console.log("🗂️ 当前ID映射表:", idMapping);
                                                generatedNodes.push(mindMapNode);
                                                callbacks.onNodeGenerated?.(mindMapNode);
                                            }
                                        } catch (e) {
                                            console.log("⏳ JSON解析失败，等待更多数据:", trimmedPart);
                                        }
                                    }
                                }
                            } catch (e) {
                                console.warn('⚠️ 解析SSE数据错误:', e, dataMatch[1]);
                            }
                        }
                    }
                }
                
                // 处理buffer中剩余的数据
                if (buffer.trim()) {
                    const remainingData = buffer.trim().replace(/END$/, '').trim();
                    if (remainingData) {
                        try {
                            const nodeData = JSON.parse(remainingData);
                            if (nodeData.id && nodeData.text && nodeData.level && nodeData.level !== 1) {
                                // 映射AI返回的parentId到实际的节点ID
                                let actualParentId = nodeData.parentId || parentNodeId;
                                if (nodeData.parentId && idMapping[nodeData.parentId]) {
                                    actualParentId = idMapping[nodeData.parentId];
                                }

                                // 生成实际的节点ID
                                const actualNodeId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                                const mindMapNode = {
                                    id: actualNodeId,
                                    text: nodeData.text,
                                    level: nodeData.level,
                                    parentId: actualParentId,
                                };

                                console.log("✅ 解析到最后一个节点:", mindMapNode);
                                generatedNodes.push(mindMapNode);
                                callbacks.onNodeGenerated?.(mindMapNode);
                            }
                        } catch (e) {
                            console.warn("⚠️ 最后一段数据解析失败:", remainingData);
                        }
                    }
                }
                
                callbacks.onEnd?.(generatedNodes);
                return generatedNodes;
                
            } catch (error) {
                console.error('💥 AI思维导图节点生成失败:', error);
                callbacks.onError?.(error instanceof Error ? error.message : '未知错误');
                throw error;
            }
        }

        async function testAiMindMapNodes() {
            const prompt = document.getElementById('testPrompt').value.trim();
            const outputDiv = document.getElementById('mindMapNodes');
            const nodesList = document.getElementById('nodesList');
            const generateBtn = document.getElementById('generateBtn');

            if (!prompt) {
                updateStatus('testStatus', '❌ 请输入测试主题', 'error');
                return;
            }

            if (isGenerating) {
                updateStatus('testStatus', '⏳ 正在生成中，请稍候...', 'info');
                return;
            }

            isGenerating = true;
            generateBtn.disabled = true;
            generatedNodes = [];

            updateStatus('testStatus', '🚀 正在连接AI服务...', 'info');
            outputDiv.style.display = 'block';
            nodesList.innerHTML = '';

            try {
                await generateAiMindMapNodes(prompt, 'root-node', {
                    onStart: () => {
                        console.log('🤖 AI开始生成思维导图节点...');
                        updateStatus('testStatus', '🤖 AI开始思考和生成节点...', 'info');
                    },

                    onNodeGenerated: (node) => {
                        console.log('📦 生成新节点:', node);
                        generatedNodes.push(node);

                        // 实时显示新生成的节点
                        const nodeDiv = document.createElement('div');
                        nodeDiv.className = `node-item level-${node.level}`;
                        nodeDiv.innerHTML = `
                            <strong>${node.text}</strong>
                            <div class="node-meta">
                                ID: ${node.id} | Level: ${node.level} | Parent: ${node.parentId || 'root'}
                            </div>
                        `;
                        nodesList.appendChild(nodeDiv);

                        // 滚动到最新节点
                        nodeDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                        updateStatus('testStatus', `📦 已生成节点: ${node.text}`, 'info');
                    },

                    onEnd: (allNodes) => {
                        console.log('✅ AI思维导图生成完成，共生成', allNodes.length, '个节点');
                        updateStatus('testStatus', `✅ 生成完成！共创建 ${allNodes.length} 个节点`, 'success');

                        // 显示统计信息
                        const statsDiv = document.createElement('div');
                        statsDiv.style.marginTop = '15px';
                        statsDiv.style.padding = '10px';
                        statsDiv.style.backgroundColor = '#e7f3ff';
                        statsDiv.style.borderRadius = '4px';

                        const level2Count = allNodes.filter(n => n.level === 2).length;
                        const level3Count = allNodes.filter(n => n.level === 3).length;

                        statsDiv.innerHTML = `
                            <strong>📊 生成统计：</strong><br>
                            Level 2 节点：${level2Count} 个<br>
                            Level 3 节点：${level3Count} 个<br>
                            总计：${allNodes.length} 个节点
                        `;
                        nodesList.appendChild(statsDiv);
                    },

                    onError: (error) => {
                        console.error('❌ 生成错误:', error);
                        updateStatus('testStatus', `❌ 生成失败: ${error}`, 'error');
                    }
                });
            } catch (error) {
                console.error('💥 测试失败:', error);
                updateStatus('testStatus', `💥 测试失败: ${error.message}`, 'error');
            } finally {
                isGenerating = false;
                generateBtn.disabled = false;
            }
        }

        function clearResults() {
            document.getElementById('mindMapNodes').style.display = 'none';
            document.getElementById('nodesList').innerHTML = '';
            document.getElementById('testStatus').innerHTML = '';
            generatedNodes = [];
            console.clear();
            console.log('🗑️ 结果已清空');
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 思维导图AI节点生成测试页面已加载');
            console.log('📖 功能说明：');
            console.log('  1. 输入主题后，AI会生成相关的思维导图节点结构');
            console.log('  2. 支持流式渲染，每生成一个节点就立即显示');
            console.log('  3. 自动建立层级关系，支持level 2和level 3节点');
            
            // 设置默认测试主题
            document.getElementById('testPrompt').value = '计算机原理';
        });
    </script>
</body>
</html>
