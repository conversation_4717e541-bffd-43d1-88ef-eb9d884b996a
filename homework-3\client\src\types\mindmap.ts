export interface MindMapNode {
  id: string;
  text: string;
  level: 1 | 2 | 3;
  parentId?: string;
  children: string[];
  collapsed?: boolean; // 是否收缩子节点，默认为false（展开）
  position: {
    x: number;
    y: number;
  };
  style: {
    fontWeight: 'normal' | 'bold';
    fontStyle: 'normal' | 'italic';
    color: string;
    borderWidth: number;
  };
}

export interface MindMapData {
  nodes: Record<string, MindMapNode>;
  rootId: string;
}

export interface ContextMenuState {
  isOpen: boolean;
  position: { x: number; y: number };
  nodeId?: string;
  nodeLevel?: number;
}

export interface SelectedNode {
  id: string;
  level: number;
}

export type MenuType = 'start' | 'style' | 'insert' | 'view' | 'export';
