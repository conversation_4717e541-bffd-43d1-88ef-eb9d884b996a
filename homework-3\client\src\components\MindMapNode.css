/* 节点容器 */
.mindmap-node-container {
  position: absolute;
}

/* 节点主体 */
.mindmap-node {
  position: relative;
  display: inline-block;
  border-width: 1px;
  border-style: solid;
  border-radius: 10px;
  cursor: pointer;
}

/* 节点选中状态 */
.mindmap-node.selected {
  box-shadow: 0 0 0 2px #067BEF;
}

/* 节点编辑状态 */
/* .mindmap-node.editing {
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.75);
} */

/* 节点文本 */
.node-text {
  word-break: break-words;
  white-space: pre-wrap;
}

/* Level 3  */
.mindmap-node.level-3 {
  /* min-width: 80px; */
  white-space: nowrap;
}

.mindmap-node.level-3 .node-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 编辑输入框 */
.node-input {
  background: transparent;
  border: none;
  outline: none;
}


/* 添加子节点按钮 */
.add-child-button {
  position: absolute;
  right: -27px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  padding: 0;
  background-color: white;
}

.add-child-button:hover {
  background-color: #eff6ff;
}

/* 添加按钮图标 */
.add-icon {
  height: 12px;
  width: 12px;
}

/* 字符计数提示 */
.char-count {
  position: absolute;
  bottom: -24px;
  left: 0;
  font-size: 12px;
  color: #6b7280;
}

.char-count .error {
  color: #ef4444;
  margin-left: 8px;
}

/* 格式化工具栏 */
.format-toolbar {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 5px;
  z-index: 1000;
}

.toolbar-content {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  gap: 2px;
}

.toolbar-button {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-icon {
  width: 16px;
  height: 16px;
}

.text-button {
  font-weight: bold;
  font-size: 14px;
}

/* 颜色选择器 */
.color-picker-container {
  position: relative;
  display: flex;
  align-items: center;
}

.color-button:hover + .color-options,
.color-options:hover {
  display: flex;
}

.color-options {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 4px;
  gap: 2px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.color-option {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.color-option:hover {
  border-color: #9ca3af;
}

.color-option.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* 按钮激活状态 */
.button-active {
  background-color: #e5e7eb !important;
}

/* 下拉箭头 */
.dropdown-arrow {
  width: 12px;
  height: 12px;
}

/* 边距 */
.margin-left-medium {
  margin-left: 4px;
}

/* 颜色选择器弹出内容 */
.popover-content-color {
  padding: 8px;
}
