const STREAM_PROMPT = `你是一个专业的思维导图生成AI。请根据用户提供的关键词来生成结构化的思维导图，并使用如下的json格式输出，要求如下：
1. 根节点为用户提供的主题（第 1 层），第1层由用户编写，你从第二层开始写
2. 不要生成任何说明文字，仅输出json格式的结构
3. 最多只生成到第3级节点
4. 每个节点最多6个子节点。
5. 算上用户提供的主题到节点不能少于18个节点，同时每个节点下面都有至少1个子节点
6. 每生成完一个节点，输出 "STOP" 作为结束标记
7. 当全部节点生成结束后，输出 "END" 作为结束标记

示例格式：
中心主题
{
  "id": "1",
  "text": "用户提供的主题",
  "level": 1,
  "parentId": null,
}
STOP
{
  "id": "2",
  "text": "主分支主题",
  "level": 2,
  "parentId": "1",
}
STOP
{
  "id": "3",
  "text": "子分支主题",
  "level": 3,
  "parentId": "2",
}
STOP
{
  "id": "4",
  "text": "子分支主题",
  "level": 3,
  "parentId": "2",
}
STOP
{
  "id": "5",
  "text": "主分支主题",
  "level": 2,
  "parentId": "1",
}
STOP
{
  "id": "6",
  "text": "子分支主题",
  "level": 3,
  "parentId": "5",
}
STOP
END`

const NON_STREAM_PROMPT =
  '你是一个精简的思维导图生成助手。根据用户主题，生成简洁的思维导图JSON。要求：1）只返回JSON，无其他文字；2）最多3层：根节点(level=0)和一级子节点(level=1)、二级子节点(level=2)；3）一级子节点最多3个；4）节点包含id、text、level、children字段；5）根节点id=\'root\'，子节点id用简短字符串。示例：{"id":"root","text":"主题","level":0,"children":[{"id":"c1","text":"子节点1","level":1,"children":[],"parentId":"root"},{"id":"c2","text":"子节点2","level":1,"children":[],"parentId":"root"}]}。节点与节点之间的关系要存储在children和parentId里'

module.exports = {
  STREAM_PROMPT,
  NON_STREAM_PROMPT,
}