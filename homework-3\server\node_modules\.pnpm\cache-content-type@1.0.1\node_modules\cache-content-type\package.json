{"name": "cache-content-type", "version": "1.0.1", "description": "Create a full Content-Type header given a MIME type or extension and catch the result", "main": "index.js", "files": ["index.js"], "scripts": {"test": "egg-bin test", "cov": "egg-bin cov", "ci": "eslint . && npm run cov"}, "dependencies": {"mime-types": "^2.1.18", "ylru": "^1.2.0"}, "devDependencies": {"egg-bin": "^4.7.1", "egg-ci": "^1.8.0", "eslint": "^5.1.0", "eslint-config-egg": "^7.0.0", "mm": "^2.2.0"}, "repository": {"type": "git", "url": "https://github.com/node-modules/cache-content-type.git"}, "keywords": ["mime", "content-type", "lru"], "engines": {"node": ">= 6.0.0"}, "ci": {"version": "6, 8, 10"}, "author": "dead_horse", "license": "MIT"}