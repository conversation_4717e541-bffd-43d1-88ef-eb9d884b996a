import { useEffect } from "react";
import TopMenu from "./components/TopMenu";
import { SecondaryMenu } from "./components/SecondaryMenu";
import MindMapCanvas from "./components/MindMapCanvas";
import ContextMenu from "./components/ContextMenu";
import { Spin } from "antd";
import { useMindMapStore } from "./store/mindMapStore";
import "./App.css";

const App = () => {
  // 使用全局状态管理
  const {
    mindMapData,
    selectedNode,
    activeMenu,
    contextMenu,
    isAiGenerating,
    setActiveMenu,
    setSelectedNode,
    setContextMenu,
    updateNode,
    addChildNode,
    addSiblingNode,
    deleteNode,
    toggleNodeCollapse,
  } = useMindMapStore();

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Delete" && selectedNode) {
        deleteNode(selectedNode.id);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedNode, deleteNode]);

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu({ ...contextMenu, isOpen: false });
  };

  // 处理右键菜单
  const handleContextMenu = (
    e: React.MouseEvent,
    nodeId: string,
    nodeLevel: number
  ) => {
    e.preventDefault();
    setContextMenu({
      isOpen: true,
      position: { x: e.clientX, y: e.clientY },
      nodeId,
      nodeLevel,
    });
  };

  return (
    <div className="app-container">
      {/* 顶部菜单 */}
      <TopMenu activeMenu={activeMenu} onMenuChange={setActiveMenu} />

      {/* 二级菜单 */}
      {activeMenu && <SecondaryMenu menuType={activeMenu} />}

      {/* 思维导图画布 */}
      <div className="canvas-container" onClick={closeContextMenu}>
        {/* AI加载状态 */}
        <MindMapCanvas
          mindMapData={mindMapData}
          selectedNode={selectedNode}
          onNodeSelect={setSelectedNode}
          onNodeUpdate={updateNode}
          onAddChild={addChildNode}
          onToggleCollapse={toggleNodeCollapse}
          onContextMenu={handleContextMenu}
        />
      </div>

      {/* 右键菜单 */}
      {contextMenu.isOpen && (
        <ContextMenu
          isOpen={contextMenu.isOpen}
          position={contextMenu.position}
          nodeId={contextMenu.nodeId}
          nodeLevel={contextMenu.nodeLevel}
          onClose={closeContextMenu}
          onAddChild={addChildNode}
          onAddSibling={addSiblingNode}
          onDeleteNode={deleteNode}
        />
      )}
    </div>
  );
};

export default App;
